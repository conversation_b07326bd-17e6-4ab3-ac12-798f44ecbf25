<img src="./.github/assets/app-icon.png" alt="Voice Assistant App Icon" width="100" height="100">

# Android Voice Assistant

This is a starter template for [LiveKit Agents](https://docs.livekit.io/agents/overview/) that provides a simple voice interface using the [LiveKit Android SDK](https://github.com/livekit/client-sdk-android).

This template is free for you to use or modify as you see fit.

## Features

- **Real-time voice communication** with AI agents
- **Voice activity detection and visualization** with animated audio bars
- **Transcription display** for both user and agent speech
- **Keyboard chat interface** for text-based communication with the AI agent
- **Modern Android UI** built with Jetpack Compose with full preview support
- **Dual communication modes**: Voice transcriptions and text messages in a unified chat interface
- **Responsive design** with proper keyboard handling and auto-scroll functionality

## Getting started

The easiest way to get this app running is with the [Sandbox for LiveKit Cloud](https://cloud.livekit.io/projects/p_/sandbox) and the [LiveKit CLI](https://docs.livekit.io/home/<USER>/cli-setup/).

First, create a new [Sandbox Token Server](https://cloud.livekit.io/projects/p_mytc7vpzfkt/sandbox/templates/token-server) for your LiveKit Cloud project.

Then, run the following command to automatically clone this template and connect it to LiveKit Cloud:

```bash
lk app create --template android-voice-assistant --sandbox <token_server_sandbox_id>
```

Build and run the app in Android Studio.

You'll also need an agent to speak with. Try our sample voice assistant agent for [Python](https://github.com/livekit-examples/voice-pipeline-agent-python), [Node.js](https://github.com/livekit-examples/voice-pipeline-agent-node), or [create your own from scratch](https://docs.livekit.io/agents/quickstart/).

> [!NOTE]
> To setup without the LiveKit CLI, clone the repository and edit the `TokenExt.kt` file to add either a `sandboxID` (if using a [Sandbox Token Server](https://cloud.livekit.io/projects/p_/sandbox/templates/token-server)), or a [manually generated](#token-generation) URL and token.

## Token generation

In a production environment, you will be responsible for developing a solution to [generate tokens for your users](https://docs.livekit.io/home/<USER>/generating-tokens/) which is integrated with your authentication solution. You should disable your sandbox token server and modify `TokenExt.kt` to use your own token server.

## Architecture & Components

### UI Components
- **ChatInput**: Text input field with send button for keyboard-based messaging
- **ChatMessageItem**: Unified message display component handling both transcriptions and text messages
- **UserTranscription**: Styled component for voice transcription bubbles
- **UserTextMessage**: Styled component for typed text message bubbles
- **AgentMessage**: Styled component for AI agent responses

### Data Management
- **ChatManager**: Composable that manages both transcription and text message state
- **ChatMessage**: Sealed class representing different message types (transcription vs. text)
- **RememberTranscriptions**: Existing transcription handling with LiveKit data streams

### Key Features
- **Compose Previews**: All UI components include `@Preview` annotations for easy development
- **Material 3 Design**: Modern theming with proper color schemes and typography
- **Keyboard Handling**: Proper IME integration with send-on-enter functionality
- **Auto-scroll**: Chat automatically scrolls to show new messages
- **Animation**: Smooth fade-in animations for new messages

## Contributing

This template is open source and we welcome contributions! Please open a PR or issue through GitHub, and don't forget to join us in the [LiveKit Community Slack](https://livekit.io/join-slack)!
