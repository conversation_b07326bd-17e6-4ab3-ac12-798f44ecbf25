package io.livekit.android.example.voiceassistant.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import io.livekit.android.example.voiceassistant.managers.*
import io.livekit.android.room.participant.Participant
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RpcPanel(
    rpcManager: RpcManager,
    participants: List<Participant>,
    modifier: Modifier = Modifier
) {
    var selectedParticipant by remember { mutableStateOf<Participant?>(null) }
    var showRpcDialog by remember { mutableStateOf(false) }
    var selectedRpcMethod by remember { mutableStateOf("") }
    
    val coroutineScope = rememberCoroutineScope()

    Column(modifier = modifier.padding(16.dp)) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "RPC Calls",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            
            IconButton(
                onClick = { showRpcDialog = true },
                enabled = participants.isNotEmpty()
            ) {
                Icon(Icons.Default.Add, contentDescription = "Make RPC Call")
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Participant selector
        if (participants.isNotEmpty()) {
            Text(
                text = "Available Participants:",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            LazyColumn(
                modifier = Modifier.height(100.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(participants) { participant ->
                    ParticipantCard(
                        participant = participant,
                        isSelected = selectedParticipant == participant,
                        onClick = { selectedParticipant = participant }
                    )
                }
            }
        } else {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No participants available",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // RPC Call History
        Text(
            text = "Recent RPC Calls:",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        LazyColumn(
            modifier = Modifier.fillMaxHeight(),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(rpcManager.rpcCalls.takeLast(10).reversed()) { rpcCall ->
                RpcCallCard(rpcCall = rpcCall)
            }
            
            if (rpcManager.rpcCalls.isEmpty()) {
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant)
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "No RPC calls yet",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }

    // RPC Method Selection Dialog
    if (showRpcDialog && selectedParticipant != null) {
        RpcMethodDialog(
            participant = selectedParticipant!!,
            onDismiss = { showRpcDialog = false },
            onMethodSelected = { method ->
                selectedRpcMethod = method
                showRpcDialog = false
                
                // Execute RPC call
                coroutineScope.launch {
                    when (method) {
                        "getUserLocation" -> {
                            rpcManager.callGetUserLocation(selectedParticipant!!.identity!!)
                        }
                        "getUserTime" -> {
                            rpcManager.callGetUserTime(selectedParticipant!!.identity!!)
                        }
                        "getDeviceInfo" -> {
                            rpcManager.callGetDeviceInfo(selectedParticipant!!.identity!!)
                        }
                        "requestPermission" -> {
                            rpcManager.callRequestPermission(
                                selectedParticipant!!.identity!!,
                                "camera",
                                "Need camera access for video call"
                            )
                        }
                    }
                }
            }
        )
    }
}

@Composable
fun ParticipantCard(
    participant: Participant,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .height(48.dp),
        onClick = onClick,
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        border = if (isSelected) {
            CardDefaults.outlinedCardBorder()
        } else null
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                Icons.Default.Person,
                contentDescription = null,
                tint = if (isSelected) {
                    MaterialTheme.colorScheme.onPrimaryContainer
                } else {
                    MaterialTheme.colorScheme.onSurface
                }
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = participant.identity?.value ?: "Unknown",
                style = MaterialTheme.typography.bodyMedium,
                color = if (isSelected) {
                    MaterialTheme.colorScheme.onPrimaryContainer
                } else {
                    MaterialTheme.colorScheme.onSurface
                }
            )
        }
    }
}

@Composable
fun RpcCallCard(
    rpcCall: RpcCall,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (rpcCall.status) {
                RpcStatus.SUCCESS -> MaterialTheme.colorScheme.surfaceVariant
                RpcStatus.ERROR -> MaterialTheme.colorScheme.errorContainer
                RpcStatus.PENDING -> MaterialTheme.colorScheme.secondaryContainer
                RpcStatus.TIMEOUT -> MaterialTheme.colorScheme.errorContainer
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = rpcCall.method,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                
                StatusChip(status = rpcCall.status)
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = "To: ${rpcCall.participant.value}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            if (rpcCall.response != null) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "Response: ${rpcCall.response.take(100)}${if (rpcCall.response.length > 100) "..." else ""}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            if (rpcCall.error != null) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "Error: ${rpcCall.error}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

@Composable
fun StatusChip(
    status: RpcStatus,
    modifier: Modifier = Modifier
) {
    val (color, text, icon) = when (status) {
        RpcStatus.SUCCESS -> Triple(Color.Green, "Success", Icons.Default.Check)
        RpcStatus.ERROR -> Triple(Color.Red, "Error", Icons.Default.Close)
        RpcStatus.PENDING -> Triple(Color.Orange, "Pending", Icons.Default.Schedule)
        RpcStatus.TIMEOUT -> Triple(Color.Red, "Timeout", Icons.Default.Timer)
    }
    
    Surface(
        modifier = modifier,
        shape = MaterialTheme.shapes.small,
        color = color.copy(alpha = 0.1f)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                icon,
                contentDescription = null,
                modifier = Modifier.size(12.dp),
                tint = color
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = text,
                style = MaterialTheme.typography.labelSmall,
                color = color
            )
        }
    }
}

@Composable
fun RpcMethodDialog(
    participant: Participant,
    onDismiss: () -> Unit,
    onMethodSelected: (String) -> Unit
) {
    val methods = listOf(
        "getUserLocation" to "Get Location",
        "getUserTime" to "Get Time",
        "getDeviceInfo" to "Get Device Info",
        "requestPermission" to "Request Permission"
    )

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("Select RPC Method")
        },
        text = {
            Column {
                Text("Call method on ${participant.identity?.value}:")
                Spacer(modifier = Modifier.height(8.dp))
                
                methods.forEach { (method, displayName) ->
                    TextButton(
                        onClick = { onMethodSelected(method) },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(displayName)
                    }
                }
            }
        },
        confirmButton = {},
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Preview(showBackground = true)
@Composable
fun RpcPanelPreview() {
    MaterialTheme {
        // Preview with mock data would go here
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Text("RPC Panel Preview")
        }
    }
}
