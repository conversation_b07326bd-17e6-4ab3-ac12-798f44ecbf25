package io.livekit.android.example.voiceassistant.datastreams

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import com.github.ajalt.timberkt.Timber
import io.livekit.android.room.Room
import io.livekit.android.room.participant.Participant
import kotlinx.coroutines.launch

private const val CHAT_TOPIC = "lk.chat"

/**
 * Manages both transcriptions and text chat messages
 */
@Composable
fun rememberChatManager(room: Room): ChatManager {
    val coroutineScope = rememberCoroutineScope()
    val messages = remember(room) { mutableStateListOf<ChatMessage>() }
    val transcriptions = rememberTranscriptions(room)
    
    // Convert transcriptions to chat messages and merge with text messages
    val allMessages = remember(transcriptions, messages) {
        derivedStateOf {
            val transcriptionMessages = transcriptions.map { it.toChatMessage() }
            val allChatMessages = (transcriptionMessages + messages).sortedBy { it.timestamp }
            allChatMessages
        }
    }

    val chatManager = remember(room) {
        ChatManager(
            room = room,
            messages = allMessages.value,
            onSendTextMessage = { text ->
                coroutineScope.launch {
                    try {
                        // Send text message through data channel
                        room.localParticipant.publishData(
                            data = text.toByteArray(),
                            topic = CHAT_TOPIC,
                            reliable = true
                        )
                        
                        // Add to local messages immediately for better UX
                        val textMessage = ChatMessage.TextMessage(
                            identity = room.localParticipant.identity,
                            text = text,
                            isFromUser = true
                        )
                        messages.add(textMessage)
                        
                        Timber.d { "Sent text message: $text" }
                    } catch (e: Exception) {
                        Timber.e(e) { "Failed to send text message" }
                    }
                }
            }
        )
    }

    // Listen for incoming text messages
    DisposableEffect(room) {
        val dataReceiver = room.dataReceived.subscribe { data ->
            try {
                if (data.topic == CHAT_TOPIC) {
                    val messageText = String(data.data)
                    val textMessage = ChatMessage.TextMessage(
                        identity = data.participant?.identity ?: Participant.Identity("unknown"),
                        text = messageText,
                        isFromUser = data.participant?.identity == room.localParticipant.identity
                    )
                    
                    // Only add if it's not from the local participant (we already added it above)
                    if (data.participant?.identity != room.localParticipant.identity) {
                        messages.add(textMessage)
                    }
                    
                    Timber.d { "Received text message: $messageText" }
                }
            } catch (e: Exception) {
                Timber.e(e) { "Failed to process received data" }
            }
        }

        onDispose {
            dataReceiver.dispose()
        }
    }

    // Update chat manager with latest messages
    return chatManager.copy(messages = allMessages.value)
}

/**
 * Chat manager that handles both transcriptions and text messages
 */
data class ChatManager(
    val room: Room,
    val messages: List<ChatMessage>,
    val onSendTextMessage: (String) -> Unit
) {
    /**
     * Get the last user message (either transcription or text)
     */
    val lastUserMessage: ChatMessage?
        get() = messages.lastOrNull { it.identity == room.localParticipant.identity }

    /**
     * Get the last agent message (either transcription or text)
     */
    val lastAgentMessage: ChatMessage?
        get() = messages.lastOrNull { it.identity != room.localParticipant.identity }

    /**
     * Get messages for display (showing only the latest from each participant)
     */
    val displayMessages: List<ChatMessage>
        get() = listOfNotNull(lastUserMessage, lastAgentMessage)

    /**
     * Get all messages chronologically
     */
    val allMessages: List<ChatMessage>
        get() = messages.sortedBy { it.timestamp }
}
