package io.livekit.android.example.voiceassistant.datastreams

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import com.github.ajalt.timberkt.Timber
import io.livekit.android.room.Room
import io.livekit.android.room.participant.Participant
import kotlinx.coroutines.launch

private const val CHAT_TOPIC = "lk.chat"

/**
 * Manages both transcriptions and text chat messages
 */
@Composable
fun rememberChatManager(room: Room): ChatManager {
    val coroutineScope = rememberCoroutineScope()
    val messages = remember(room) { mutableStateListOf<ChatMessage>() }
    val transcriptions = rememberTranscriptions(room)

    // Convert transcriptions to chat messages and merge with text messages
    val allMessages = remember(transcriptions, messages) {
        derivedStateOf {
            val transcriptionMessages = transcriptions.map { it.toChatMessage() }
            val allChatMessages = (transcriptionMessages + messages).sortedBy { it.timestamp }
            Timber.d { "Total messages: transcriptions=${transcriptionMessages.size}, text=${messages.size}, combined=${allChatMessages.size}" }
            allChatMessages
        }
    }

    val chatManager = remember(room) {
        ChatManager(
            room = room,
            messages = allMessages.value,
            onSendTextMessage = { text ->
                coroutineScope.launch {
                    try {
                        // Add to local messages immediately for better UX
                        val textMessage = ChatMessage.TextMessage(
                            identity = room.localParticipant.identity ?: Participant.Identity("user"),
                            text = text,
                            isFromUser = true
                        )
                        messages.add(textMessage)

                        // Try to send via text stream (similar to transcriptions)
                        // For now, we'll just log the attempt - in a real implementation,
                        // you would need to set up a text stream publisher
                        Timber.d { "Added text message locally: $text" }
                        Timber.i { "Note: Text message sending via LiveKit data channels not yet implemented" }

                    } catch (e: Exception) {
                        Timber.e(e) { "Failed to add text message: ${e.message}" }
                    }
                }
            }
        )
    }

    // Listen for incoming chat messages using text stream handler
    DisposableEffect(room) {
        room.registerTextStreamHandler(CHAT_TOPIC) { receiver, identity ->
            coroutineScope.launch {
                try {
                    // Collect the incoming chat message stream
                    receiver.flow.collect { messageText ->
                        val textMessage = ChatMessage.TextMessage(
                            identity = identity,
                            text = messageText,
                            isFromUser = identity == room.localParticipant.identity
                        )

                        // Only add if it's not from the local participant (we already added it above)
                        if (identity != room.localParticipant.identity) {
                            messages.add(textMessage)
                            Timber.d { "Received text message from $identity: $messageText" }
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e) { "Failed to process received chat message" }
                }
            }
        }

        onDispose {
            // Clean up the handler when done with it
            room.unregisterTextStreamHandler(CHAT_TOPIC)
        }
    }

    // Update chat manager with latest messages
    return chatManager.copy(messages = allMessages.value)
}

/**
 * Chat manager that handles both transcriptions and text messages
 */
data class ChatManager(
    val room: Room,
    val messages: List<ChatMessage>,
    val onSendTextMessage: (String) -> Unit
) {
    /**
     * Get the last user message (either transcription or text)
     */
    val lastUserMessage: ChatMessage?
        get() = messages.lastOrNull { it.identity == (room.localParticipant.identity ?: Participant.Identity("user")) }

    /**
     * Get the last agent message (either transcription or text)
     */
    val lastAgentMessage: ChatMessage?
        get() = messages.lastOrNull { it.identity != (room.localParticipant.identity ?: Participant.Identity("user")) }

    /**
     * Get messages for display (showing only the latest from each participant)
     */
    val displayMessages: List<ChatMessage>
        get() = listOfNotNull(lastUserMessage, lastAgentMessage)

    /**
     * Get all messages chronologically
     */
    val allMessages: List<ChatMessage>
        get() = messages.sortedBy { it.timestamp }
}
