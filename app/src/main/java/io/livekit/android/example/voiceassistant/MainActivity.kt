@file:OptIn(Beta::class)

package io.livekit.android.example.voiceassistant

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.ajalt.timberkt.Timber
import io.livekit.android.LiveKit
import io.livekit.android.annotations.Beta
import io.livekit.android.compose.local.RoomScope
import io.livekit.android.compose.state.rememberVoiceAssistant
import io.livekit.android.compose.ui.audio.VoiceAssistantBarVisualizer
import io.livekit.android.example.voiceassistant.datastreams.rememberChatManager
import io.livekit.android.example.voiceassistant.ui.ChatInput
import io.livekit.android.example.voiceassistant.ui.ChatMessageItem
import io.livekit.android.example.voiceassistant.ui.theme.LiveKitVoiceAssistantExampleTheme
import io.livekit.android.util.LoggingLevel

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LiveKit.loggingLevel = LoggingLevel.DEBUG
        requireNeededPermissions {
            requireToken { url, token ->
                setContent {
                    LiveKitVoiceAssistantExampleTheme(dynamicColor = false) {
                        Surface {
                            VoiceAssistant(
                                url,
                                token,
                                modifier = Modifier
                                    .fillMaxSize()
                            )
                        }
                    }
                }
            }
        }
    }

    @Composable
    fun VoiceAssistant(url: String, token: String, modifier: Modifier = Modifier) {
        RoomScope(
            url,
            token,
            audio = true,
            connect = true,
        ) { room ->
            ConstraintLayout(modifier = modifier.fillMaxSize()) {
                val (audioVisualizer, chatLog, chatInput) = createRefs()
                val voiceAssistant = rememberVoiceAssistant()
                val chatManager = rememberChatManager(room)

                val agentState = voiceAssistant.state
                // Optionally do something with the agent state.
                LaunchedEffect(key1 = agentState) {
                    Timber.i { "agent state: $agentState" }
                }

                // Amplitude visualization of the Assistant's voice track.
                VoiceAssistantBarVisualizer(
                    voiceAssistant = voiceAssistant,
                    modifier = Modifier
                        .padding(8.dp)
                        .fillMaxWidth()
                        .constrainAs(audioVisualizer) {
                            height = Dimension.percent(0.08f)
                            width = Dimension.percent(0.8f)

                            top.linkTo(parent.top, 8.dp)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        },
                    brush = SolidColor(MaterialTheme.colorScheme.onBackground)
                )

                // Chat display
                val lazyListState = rememberLazyListState()
                val displayMessages = chatManager.displayMessages

                // Auto-scroll to bottom when new messages arrive
                LaunchedEffect(displayMessages.size) {
                    if (displayMessages.isNotEmpty()) {
                        lazyListState.animateScrollToItem(displayMessages.size - 1)
                    }
                }

                LazyColumn(
                    userScrollEnabled = true,
                    state = lazyListState,
                    modifier = Modifier
                        .constrainAs(chatLog) {
                            top.linkTo(audioVisualizer.bottom, 8.dp)
                            bottom.linkTo(chatInput.top, 8.dp)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                            width = Dimension.fillToConstraints
                            height = Dimension.fillToConstraints
                        }
                ) {
                    items(
                        items = displayMessages,
                        key = { message -> message.id },
                    ) { message ->
                        ChatMessageItem(
                            message = message,
                            isFromUser = message.identity == room.localParticipant.identity,
                            modifier = Modifier.animateItem()
                        )
                    }
                }

                // Chat input at the bottom
                ChatInput(
                    onSendMessage = chatManager.onSendTextMessage,
                    modifier = Modifier
                        .constrainAs(chatInput) {
                            bottom.linkTo(parent.bottom)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                            width = Dimension.fillToConstraints
                        }
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun VoiceAssistantPreview() {
    LiveKitVoiceAssistantExampleTheme {
        Surface {
            // Note: This is just a UI preview - actual functionality requires LiveKit connection
            Box(modifier = Modifier.fillMaxSize()) {
                ChatInput(
                    onSendMessage = { /* Preview - no action */ },
                    modifier = Modifier.align(androidx.compose.ui.Alignment.BottomCenter)
                )
            }
        }
    }
}
