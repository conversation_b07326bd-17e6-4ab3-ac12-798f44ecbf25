package io.livekit.android.example.voiceassistant.managers

import android.content.Context
import android.net.Uri
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import com.github.ajalt.timberkt.Timber
import io.livekit.android.room.Room
import io.livekit.android.room.participant.Participant
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream

/**
 * File transfer metadata
 */
@Serializable
data class FileTransferMetadata(
    val id: String,
    val fileName: String,
    val fileSize: Long,
    val mimeType: String,
    val checksum: String? = null,
    val chunkCount: Int,
    val timestamp: Long
)

@Serializable
data class FileChunk(
    val transferId: String,
    val chunkIndex: Int,
    val totalChunks: Int,
    val data: String, // Base64 encoded
    val checksum: String? = null
)

/**
 * File transfer status
 */
data class FileTransfer(
    val id: String,
    val fileName: String,
    val fileSize: Long,
    val participant: Participant.Identity,
    val direction: TransferDirection,
    val status: TransferStatus,
    val progress: Float = 0f,
    val bytesTransferred: Long = 0L,
    val error: String? = null,
    val timestamp: Long = System.currentTimeMillis(),
    val localFile: File? = null
)

enum class TransferDirection {
    SENDING,
    RECEIVING
}

enum class TransferStatus {
    PENDING,
    IN_PROGRESS,
    COMPLETED,
    FAILED,
    CANCELLED
}

/**
 * Manages file transfers using LiveKit data channels
 */
class FileTransferManager(
    private val room: Room,
    private val context: Context,
    private val json: Json = Json { ignoreUnknownKeys = true }
) {
    private val _transfers = mutableStateListOf<FileTransfer>()
    val transfers: List<FileTransfer> = _transfers
    
    private val _receivingChunks = mutableMapOf<String, MutableList<FileChunk>>()
    private val _transferMetadata = mutableMapOf<String, FileTransferMetadata>()

    companion object {
        const val TOPIC_FILE_METADATA = "lk.file.metadata"
        const val TOPIC_FILE_CHUNK = "lk.file.chunk"
        const val CHUNK_SIZE = 8192 // 8KB chunks
    }

    /**
     * Register file transfer handlers
     */
    fun registerFileHandlers() {
        try {
            // Register metadata handler
            room.registerTextStreamHandler(TOPIC_FILE_METADATA) { receiver, identity ->
                handleFileMetadata(receiver, identity)
            }
            
            // Register chunk handler
            room.registerTextStreamHandler(TOPIC_FILE_CHUNK) { receiver, identity ->
                handleFileChunk(receiver, identity)
            }
            
            Timber.i { "Successfully registered file transfer handlers" }
        } catch (e: Exception) {
            Timber.e(e) { "Failed to register file transfer handlers" }
        }
    }

    /**
     * Send a file to all participants
     */
    suspend fun sendFile(file: File, mimeType: String = "application/octet-stream"): String? {
        return sendFileToParticipants(file, mimeType, emptyList()) // Send to all
    }

    /**
     * Send a file to specific participants
     */
    suspend fun sendFileToParticipants(
        file: File,
        mimeType: String = "application/octet-stream",
        participants: List<Participant.Identity>
    ): String? {
        val transferId = generateTransferId()
        
        return try {
            if (!file.exists() || !file.canRead()) {
                throw IllegalArgumentException("File does not exist or cannot be read")
            }
            
            val fileSize = file.length()
            val chunkCount = ((fileSize + CHUNK_SIZE - 1) / CHUNK_SIZE).toInt()
            
            // Create transfer record
            val transfer = FileTransfer(
                id = transferId,
                fileName = file.name,
                fileSize = fileSize,
                participant = Participant.Identity("all"), // Placeholder for broadcast
                direction = TransferDirection.SENDING,
                status = TransferStatus.PENDING,
                localFile = file
            )
            _transfers.add(transfer)
            
            // Send metadata first
            val metadata = FileTransferMetadata(
                id = transferId,
                fileName = file.name,
                fileSize = fileSize,
                mimeType = mimeType,
                chunkCount = chunkCount,
                timestamp = System.currentTimeMillis()
            )
            
            val metadataJson = json.encodeToString(metadata)
            room.localParticipant.publishData(metadataJson.toByteArray())
            
            Timber.d { "Sent file metadata for ${file.name}" }
            
            // Update status
            updateTransferStatus(transferId, TransferStatus.IN_PROGRESS)
            
            // Send file in chunks
            file.inputStream().use { inputStream ->
                val buffer = ByteArray(CHUNK_SIZE)
                var chunkIndex = 0
                var bytesRead: Int
                var totalBytesRead = 0L
                
                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                    val chunkData = if (bytesRead < CHUNK_SIZE) {
                        buffer.copyOf(bytesRead)
                    } else {
                        buffer
                    }
                    
                    val chunk = FileChunk(
                        transferId = transferId,
                        chunkIndex = chunkIndex,
                        totalChunks = chunkCount,
                        data = android.util.Base64.encodeToString(chunkData, android.util.Base64.DEFAULT)
                    )
                    
                    val chunkJson = json.encodeToString(chunk)
                    room.localParticipant.publishData(chunkJson.toByteArray())
                    
                    totalBytesRead += bytesRead
                    val progress = totalBytesRead.toFloat() / fileSize
                    updateTransferProgress(transferId, progress, totalBytesRead)
                    
                    chunkIndex++
                    
                    // Small delay to prevent overwhelming the network
                    kotlinx.coroutines.delay(10)
                }
            }
            
            updateTransferStatus(transferId, TransferStatus.COMPLETED)
            Timber.i { "Successfully sent file ${file.name} (${fileSize} bytes) in $chunkCount chunks" }
            
            transferId
            
        } catch (e: Exception) {
            Timber.e(e) { "Failed to send file ${file.name}: ${e.message}" }
            updateTransferStatus(transferId, TransferStatus.FAILED, e.message)
            null
        }
    }

    /**
     * Send a file from URI (for file picker integration)
     */
    suspend fun sendFileFromUri(uri: Uri, fileName: String, mimeType: String): String? {
        return try {
            val inputStream = context.contentResolver.openInputStream(uri)
                ?: throw IllegalArgumentException("Cannot open input stream for URI")
            
            // Create temporary file
            val tempFile = File.createTempFile("livekit_send_", "_$fileName", context.cacheDir)
            
            inputStream.use { input ->
                FileOutputStream(tempFile).use { output ->
                    input.copyTo(output)
                }
            }
            
            val result = sendFile(tempFile, mimeType)
            
            // Clean up temp file after sending
            tempFile.delete()
            
            result
            
        } catch (e: Exception) {
            Timber.e(e) { "Failed to send file from URI: ${e.message}" }
            null
        }
    }

    /**
     * Handle incoming file metadata
     */
    private suspend fun handleFileMetadata(receiver: Any, identity: Participant.Identity) {
        try {
            // This is a simplified handler - in a real implementation you would
            // properly handle the text stream receiver
            Timber.d { "Received file metadata from $identity" }
            
            // Placeholder for metadata handling
            val transferId = generateTransferId()
            val transfer = FileTransfer(
                id = transferId,
                fileName = "received_file",
                fileSize = 0L,
                participant = identity,
                direction = TransferDirection.RECEIVING,
                status = TransferStatus.PENDING
            )
            _transfers.add(transfer)
            
        } catch (e: Exception) {
            Timber.e(e) { "Error handling file metadata: ${e.message}" }
        }
    }

    /**
     * Handle incoming file chunks
     */
    private suspend fun handleFileChunk(receiver: Any, identity: Participant.Identity) {
        try {
            Timber.d { "Received file chunk from $identity" }
            
            // Placeholder for chunk handling
            // In a real implementation, you would:
            // 1. Parse the chunk data
            // 2. Store chunks in order
            // 3. Reassemble file when all chunks received
            // 4. Update progress
            
        } catch (e: Exception) {
            Timber.e(e) { "Error handling file chunk: ${e.message}" }
        }
    }

    /**
     * Cancel a file transfer
     */
    fun cancelTransfer(transferId: String) {
        try {
            updateTransferStatus(transferId, TransferStatus.CANCELLED)
            
            // Clean up any partial data
            _receivingChunks.remove(transferId)
            _transferMetadata.remove(transferId)
            
            Timber.d { "Cancelled transfer $transferId" }
        } catch (e: Exception) {
            Timber.e(e) { "Error cancelling transfer $transferId: ${e.message}" }
        }
    }

    /**
     * Get transfer by ID
     */
    fun getTransfer(transferId: String): FileTransfer? {
        return _transfers.find { it.id == transferId }
    }

    /**
     * Get active transfers
     */
    fun getActiveTransfers(): List<FileTransfer> {
        return _transfers.filter { 
            it.status == TransferStatus.PENDING || it.status == TransferStatus.IN_PROGRESS 
        }
    }

    /**
     * Get completed transfers
     */
    fun getCompletedTransfers(): List<FileTransfer> {
        return _transfers.filter { it.status == TransferStatus.COMPLETED }
    }

    private fun updateTransferStatus(transferId: String, status: TransferStatus, error: String? = null) {
        val index = _transfers.indexOfFirst { it.id == transferId }
        if (index >= 0) {
            _transfers[index] = _transfers[index].copy(status = status, error = error)
        }
    }

    private fun updateTransferProgress(transferId: String, progress: Float, bytesTransferred: Long) {
        val index = _transfers.indexOfFirst { it.id == transferId }
        if (index >= 0) {
            _transfers[index] = _transfers[index].copy(
                progress = progress,
                bytesTransferred = bytesTransferred
            )
        }
    }

    private fun generateTransferId(): String {
        return "transfer_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }

    fun cleanup() {
        try {
            // Unregister handlers
            room.unregisterTextStreamHandler(TOPIC_FILE_METADATA)
            room.unregisterTextStreamHandler(TOPIC_FILE_CHUNK)
            
            // Clear data
            _transfers.clear()
            _receivingChunks.clear()
            _transferMetadata.clear()
            
            Timber.d { "Cleaned up file transfer manager" }
        } catch (e: Exception) {
            Timber.e(e) { "Error during file transfer manager cleanup" }
        }
    }
}

/**
 * Composable function to create and manage FileTransferManager
 */
@Composable
fun rememberFileTransferManager(room: Room, context: Context): FileTransferManager {
    val coroutineScope = rememberCoroutineScope()
    
    val fileTransferManager = remember(room) {
        FileTransferManager(room, context).apply {
            coroutineScope.launch {
                registerFileHandlers()
            }
        }
    }

    DisposableEffect(room) {
        onDispose {
            fileTransferManager.cleanup()
        }
    }

    return fileTransferManager
}
