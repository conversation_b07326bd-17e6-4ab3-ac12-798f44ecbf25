package io.livekit.android.example.voiceassistant.managers

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import com.github.ajalt.timberkt.Timber
import io.livekit.android.room.Room
import io.livekit.android.room.participant.Participant
import kotlinx.coroutines.launch

/**
 * Event types for UI display
 */
sealed class LiveKitEvent {
    data class ParticipantConnected(
        val participant: Participant,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class ParticipantDisconnected(
        val participant: Participant,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class ConnectionStateChanged(
        val state: String,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class TrackPublished(
        val participant: Participant,
        val trackType: String,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class TrackUnpublished(
        val participant: Participant,
        val trackType: String,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class DataReceived(
        val participant: Participant,
        val dataSize: Int,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class RoomMetadataChanged(
        val metadata: String,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class ParticipantMetadataChanged(
        val participant: Participant,
        val metadata: String,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class ConnectionQualityChanged(
        val participant: Participant,
        val quality: String,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class ActiveSpeakersChanged(
        val speakers: List<Participant>,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class ErrorOccurred(
        val error: String,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
}

/**
 * Manages LiveKit room and participant events
 */
class EventManager(
    private val room: Room
) {
    private val _events = mutableStateListOf<LiveKitEvent>()
    val events: List<LiveKitEvent> = _events
    
    private val _connectionState = mutableStateListOf<String>()
    val connectionState: List<String> = _connectionState
    
    private val _participants = mutableStateListOf<Participant>()
    val participants: List<Participant> = _participants

    /**
     * Start listening to room events
     */
    fun startEventListening() {
        try {
            Timber.i { "Starting LiveKit event listening" }
            
            // Initialize participants list
            _participants.clear()
            _participants.addAll(room.remoteParticipants.values)
            if (room.localParticipant != null) {
                _participants.add(room.localParticipant)
            }
            
            addEvent(LiveKitEvent.ConnectionStateChanged("Event listening started"))
            
        } catch (e: Exception) {
            Timber.e(e) { "Failed to start event listening" }
            addEvent(LiveKitEvent.ErrorOccurred("Failed to start event listening: ${e.message}"))
        }
    }

    /**
     * Handle room events using coroutines
     */
    suspend fun handleRoomEvents() {
        try {
            room.events.collect { event ->
                when (event) {
                    is RoomEvent.ParticipantConnected -> {
                        Timber.d { "Participant connected: ${event.participant.identity}" }
                        _participants.add(event.participant)
                        addEvent(LiveKitEvent.ParticipantConnected(event.participant))
                    }
                    
                    is RoomEvent.ParticipantDisconnected -> {
                        Timber.d { "Participant disconnected: ${event.participant.identity}" }
                        _participants.removeAll { it.identity == event.participant.identity }
                        addEvent(LiveKitEvent.ParticipantDisconnected(event.participant))
                    }
                    
                    is RoomEvent.Reconnecting -> {
                        Timber.d { "Room reconnecting" }
                        addEvent(LiveKitEvent.ConnectionStateChanged("Reconnecting"))
                    }
                    
                    is RoomEvent.Reconnected -> {
                        Timber.d { "Room reconnected" }
                        addEvent(LiveKitEvent.ConnectionStateChanged("Reconnected"))
                    }
                    
                    is RoomEvent.Disconnected -> {
                        Timber.d { "Room disconnected: ${event.reason}" }
                        addEvent(LiveKitEvent.ConnectionStateChanged("Disconnected: ${event.reason}"))
                    }
                    
                    is RoomEvent.TrackPublished -> {
                        Timber.d { "Track published: ${event.publication.trackInfo.name}" }
                        addEvent(LiveKitEvent.TrackPublished(
                            participant = event.participant,
                            trackType = event.publication.trackInfo.type.toString()
                        ))
                    }
                    
                    is RoomEvent.TrackUnpublished -> {
                        Timber.d { "Track unpublished: ${event.publication.trackInfo.name}" }
                        addEvent(LiveKitEvent.TrackUnpublished(
                            participant = event.participant,
                            trackType = event.publication.trackInfo.type.toString()
                        ))
                    }
                    
                    is RoomEvent.DataReceived -> {
                        Timber.d { "Data received from ${event.participant?.identity}: ${event.data.size} bytes" }
                        event.participant?.let { participant ->
                            addEvent(LiveKitEvent.DataReceived(
                                participant = participant,
                                dataSize = event.data.size
                            ))
                        }
                    }
                    
                    is RoomEvent.RoomMetadataChanged -> {
                        Timber.d { "Room metadata changed: ${event.metadata}" }
                        addEvent(LiveKitEvent.RoomMetadataChanged(event.metadata ?: ""))
                    }
                    
                    is RoomEvent.ParticipantMetadataChanged -> {
                        Timber.d { "Participant metadata changed: ${event.participant.identity}" }
                        addEvent(LiveKitEvent.ParticipantMetadataChanged(
                            participant = event.participant,
                            metadata = event.participant.metadata ?: ""
                        ))
                    }
                    
                    is RoomEvent.ConnectionQualityChanged -> {
                        Timber.d { "Connection quality changed for ${event.participant.identity}: ${event.quality}" }
                        addEvent(LiveKitEvent.ConnectionQualityChanged(
                            participant = event.participant,
                            quality = event.quality.toString()
                        ))
                    }
                    
                    is RoomEvent.ActiveSpeakersChanged -> {
                        Timber.d { "Active speakers changed: ${event.speakers.size} speakers" }
                        addEvent(LiveKitEvent.ActiveSpeakersChanged(event.speakers))
                    }
                    
                    else -> {
                        Timber.d { "Unhandled room event: ${event::class.simpleName}" }
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e) { "Error handling room events: ${e.message}" }
            addEvent(LiveKitEvent.ErrorOccurred("Event handling error: ${e.message}"))
        }
    }

    /**
     * Get current room statistics
     */
    fun getRoomStats(): Map<String, Any> {
        return try {
            mapOf(
                "participantCount" to _participants.size,
                "localParticipant" to (room.localParticipant?.identity?.value ?: "unknown"),
                "connectionState" to room.connectionState.toString(),
                "eventCount" to _events.size,
                "roomName" to (room.name ?: "unknown"),
                "roomSid" to (room.sid ?: "unknown")
            )
        } catch (e: Exception) {
            Timber.e(e) { "Error getting room stats" }
            mapOf("error" to e.message.orEmpty())
        }
    }

    /**
     * Get participant by identity
     */
    fun getParticipant(identity: Participant.Identity): Participant? {
        return _participants.find { it.identity == identity }
    }

    /**
     * Get all remote participants
     */
    fun getRemoteParticipants(): List<Participant> {
        return _participants.filter { it != room.localParticipant }
    }

    /**
     * Clear old events to prevent memory issues
     */
    fun clearOldEvents(maxEvents: Int = 100) {
        if (_events.size > maxEvents) {
            val eventsToRemove = _events.size - maxEvents
            repeat(eventsToRemove) {
                _events.removeAt(0)
            }
            Timber.d { "Cleared $eventsToRemove old events" }
        }
    }

    private fun addEvent(event: LiveKitEvent) {
        _events.add(event)
        
        // Auto-clear old events
        clearOldEvents()
        
        Timber.v { "Added event: ${event::class.simpleName}" }
    }

    fun cleanup() {
        try {
            _events.clear()
            _connectionState.clear()
            _participants.clear()
            
            Timber.d { "Cleaned up event manager" }
        } catch (e: Exception) {
            Timber.e(e) { "Error during event manager cleanup" }
        }
    }
}

/**
 * Composable function to create and manage EventManager
 */
@Composable
fun rememberEventManager(room: Room): EventManager {
    val coroutineScope = rememberCoroutineScope()
    
    val eventManager = remember(room) {
        EventManager(room).apply {
            startEventListening()
            coroutineScope.launch {
                handleRoomEvents()
            }
        }
    }

    DisposableEffect(room) {
        onDispose {
            eventManager.cleanup()
        }
    }

    return eventManager
}
